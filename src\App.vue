<script setup lang="ts">
import autofit from "autofit.js";
import { elRectification } from "autofit.js";
import { computed, onUnmounted, onMounted } from "vue";
import { NConfigProvider, darkTheme } from "naive-ui";
import { useAppStore } from "./store/modules/app";
import { useThemeStore } from "./store/modules/theme";
import { naiveDateLocales, naiveLocales } from "./locales/naive";
import { useScreenLock } from "@/store/screenlock";
defineOptions({
  name: "App",
});
autofit.init({
  dh: 1080,
  dw: 1920,
  el: "#app",
  resize: true,
  // ignore: [".el-popper",".el-select",".el-scrollbar",".el-select-dropdown"],
});
const appStore = useAppStore();
const themeStore = useThemeStore();
const screenLockStore = useScreenLock(); // 状态修改
const naiveDarkTheme = computed(() =>
  themeStore.darkMode ? darkTheme : undefined,
);

const naiveLocale = computed(() => {
  return naiveLocales[appStore.locale];
});

const naiveDateLocale = computed(() => {
  return naiveDateLocales[appStore.locale];
});

onUnmounted(() => {
  screenLockStore.dispose();
});
onMounted(() => {
  elRectification('div[id*="el-popper-container"]');
});
const onSureBtnClick = () => {
  screenLockStore.checkPwd();
};
</script>

<template>
  <NConfigProvider
    :theme="naiveDarkTheme"
    :theme-overrides="themeStore.naiveTheme"
    :locale="naiveLocale"
    :date-locale="naiveDateLocale"
    class="h-full"
  >
    <AppProvider>
      <RouterView class="bg-layout" />
    </AppProvider>
  </NConfigProvider>

  <div class="masking-out" v-show="screenLockStore.isLocked">
    <div class="confirm-bg">
      <div class="row">
        <span>解锁密码</span>
        <el-input
          class="input"
          v-model="screenLockStore.inputPwd"
          placeholder="请输入"
          type="password"
          show-password
          maxlength="20"
          @keydown.enter="onSureBtnClick"
        ></el-input>
        <div class="bottom-con">
          <el-button class="btn" type="primary" @click="onSureBtnClick"
            >确定</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.masking-out {
  width: 100vw;
  height: 100vh;
  background: hsla(0, 0%, 0%, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .confirm-bg {
    width: 400px;
    height: 140px;
    background: #fff;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;

    .row {
      width: 100%;
      padding: 20px;

      .input {
        margin-top: 6px;
      }

      .bottom-con {
        margin-top: 10px;
        .btn {
          float: right;
        }
      }
    }
  }
}
</style>
